# Underlines ADK Development Roadmap

## Task Tracking and Progress Management

<!-- purpose-start -->
Comprehensive task tracking for Underlines ADK development, organized by phases and priorities. Each task represents approximately 1 day of work with clear acceptance criteria and dependencies.
<!-- purpose-end -->

---

## Current Status (2025-01-07)

### ✅ Completed

- [x] **DeepSearchAgent Implementation** - Comprehensive search with citations
- [x] **Context Window Optimization** - Fixed with Gemini Flash and optimized result counts
- [x] **Comprehensive Testing Suite** - Unit and integration tests passing
- [x] **Project Documentation** - README, architecture, and agent patterns documented
- [x] **PRD and Tech Spec Creation** - Product requirements and technical specifications defined
- [x] **Critical Architecture Documentation Updates** - Added pipeline flexibility requirements
- [x] **Flexible Newsletter Pipeline Design** - Comprehensive design for universal newsletter generation
- [x] **Pipeline Flexibility Requirements Documentation** - Critical guidelines to prevent hardcoding

### 🚨 CRITICAL PRIORITY: Flexible Newsletter Generation Pipeline

**URGENT**: Implement universal newsletter generation pipeline that handles ANY newsletter type based on natural language requirements. This is the core capability that validates our entire architectural approach.

**Key Requirements**:

- ✅ Universal natural language interface (no hardcoded formats)
- ✅ Dynamic agent creation based on user requirements
- ✅ Quality assessment against user's stated expectations
- ✅ ADK-native patterns (SequentialAgent, ParallelAgent, LoopAgent)
- ✅ Agentic principles (trust LLM intelligence over programmatic validation)

**Reference Documents**:

- [PRD.md](../.docs/PRD.md) - Product vision and requirements
- [EXISTING_AGENTS.md](../.docs/EXISTING_AGENTS.md) - WideSearchAgent & DeepSearchAgent capabilities
- [BUILDING_ORCHESTRATORS.md](../.docs/BUILDING_ORCHESTRATORS.md) - Implementation patterns and code examples

**✅ Existing Assets to Leverage (Integration Strategy Analysis)**:

- `underlines_adk/agents/wide_search_agent.py` - WideSearchAgent (EXCELLENT: already perfectly flexible for any newsletter type)
- `underlines_adk/agents/deep_search_agent.py` - DeepSearchAgent (EXCELLENT: already perfectly flexible for any newsletter type)
- `underlines_adk/tools/exa_tools.py` - exa_wide_search and exa_deep_search tools (EXCELLENT: work with any search query)

**✅ Integration Strategy Decision**: **Option C - Orchestrator/Wrapper Approach**

- **Rationale**: Existing agents are already perfectly designed for flexible pipeline
- **Approach**: Create orchestration layers that coordinate existing agents, NO duplication or modification
- **Benefits**: Leverage proven quality, maintain backward compatibility, minimize development effort
- **Pattern**: ResearchPlanner → DynamicResearchCoordinator → FlexibleNewsletterSynthesizer orchestrating existing agents

**Key Insight**: Our existing agents work with any topic/query without hardcoded newsletter-specific logic - we only need orchestration, not new research capabilities

---

## Phase 1: Flexible Newsletter Generation Pipeline (Q1 2025)

### Step 0: Research & Planning (1 day) ✅ COMPLETED

- [x] **Google ADK Multi-Agent Documentation Review**
  - [x] Study loop agent patterns via Context7 MCP server
  - [x] Study parallel agent patterns via Context7 MCP server
  - [x] Use context7 to gather comprehensive Google ADK documentation
  - [x] Document key patterns and implementation approaches
  - [x] Create comprehensive architectural documentation
  - **Acceptance Criteria**: ✅ Clear understanding of Google ADK multi-agent capabilities
  - **Dependencies**: None
  - **Deliverables**:
    - ✅ Created `.docs/README.md` with ADK-native quick start
    - ✅ Created `.docs/ARCHITECTURE.md` with ADK workflow patterns
    - ✅ Created `.docs/IMPLEMENTATION_GUIDE.md` with practical ADK patterns
    - ✅ Created `.docs/DEVELOPMENT_GUIDE.md` with ADK development workflows
    - ✅ Created `.docs/PRODUCT_SPECIFICATION.md` with combined requirements
    - ✅ Created `.docs/MIGRATION_TRACKING.md` with content migration plan
    - ✅ Identified 22 anti-agentic patterns for removal
    - ✅ Mapped all patterns to ADK-native equivalents

- [x] **Documentation Restructuring Day 2** - ADK pattern implementation and content migration
  - [x] Enhanced IMPLEMENTATION_GUIDE.md with comprehensive ADK patterns
  - [x] Migrated valuable content from AGENT_PATTERNS.md
  - [x] Added instruction patterns, error handling, and testing strategies
  - [x] Enhanced DEVELOPMENT_GUIDE.md with workflow content from WORKFLOWS.md
  - [x] Added ADK-native development workflows and deployment patterns
  - [x] Replaced anti-agentic patterns with tool-based escalation examples
  - [x] Added comprehensive testing patterns for LoopAgent, ParallelAgent, SequentialAgent
  - **Acceptance Criteria**: ✅ Enhanced documentation with practical ADK implementation patterns
  - **Dependencies**: Day 1 completion
  - **Deliverables**:
    - ✅ Enhanced IMPLEMENTATION_GUIDE.md with 765+ lines of ADK patterns
    - ✅ Enhanced DEVELOPMENT_GUIDE.md with comprehensive workflows
    - ✅ Added tool-based escalation examples throughout
    - ✅ Migrated best practices from AGENT_PATTERNS.md and WORKFLOWS.md

- [x] **Critical Architecture Corrections** - Pipeline flexibility requirements and design
  - [x] Identified fundamental misunderstanding in hardcoding daily news specifics
  - [x] Created comprehensive flexible newsletter pipeline design
  - [x] Updated `.docs/ARCHITECTURE.md` with critical flexibility requirements
  - [x] Updated `.docs/PRODUCT_SPECIFICATION.md` with universal interface requirements
  - [x] Created `.docs/PIPELINE_FLEXIBILITY_REQUIREMENTS.md` with implementation guidelines
  - [x] Designed universal natural language interface for ANY newsletter type
  - **Acceptance Criteria**: ✅ Clear architectural foundation for flexible newsletter generation
  - **Dependencies**: Documentation restructuring completion
  - **Deliverables**:
    - ✅ Created `.tasks/backlog/daily_news_use_case_design.md` with flexible pipeline design
    - ✅ Added critical "NEVER HARDCODE" vs "ALWAYS MAKE FLEXIBLE" guidelines
    - ✅ Established validation checklists for all agent instructions
    - ✅ Prevented future hardcoding of newsletter-specific formats

### Step 1: Flexible Newsletter Generation Pipeline Implementation (5 days)

🚨 **CRITICAL**: This implementation leverages existing WideSearchAgent and DeepSearchAgent through orchestration layers, following the integration strategy analysis. Must adhere to guidelines in `.docs/PIPELINE_FLEXIBILITY_REQUIREMENTS.md`

**Integration Strategy**: Use existing agents through orchestrator/wrapper approach - NO duplication or modification of existing agents.

#### Day 1: Natural Language Research Planning with Existing Agents ✅ COMPLETED

- [x] **Implement ResearchPlanner LlmAgent as Orchestrator**
  - [x] Create ResearchPlanner that coordinates existing WideSearchAgent and DeepSearchAgent instances
  - [x] Use exa_wide_search tool for initial topic discovery and planning
  - [x] Implement instruction that analyzes user requirements without extracting structured data
  - [x] Plan research workflows that leverage existing agent capabilities based on user's natural language requirements
  - [x] Test coordination with diverse natural language inputs (daily news, biotech VC, fintech examples)
  - [x] Ensure research planning adapts to ANY newsletter type using existing agent flexibility
  - [x] Validate against flexibility requirements checklist
  - [x] Ensure tests pass and update TODOS.md
  - **Acceptance Criteria**: ✅ ResearchPlanner orchestrates existing agents for any newsletter type
  - **Dependencies**: Architecture documentation completed
  - **Key Pattern**: Orchestration layer over existing WideSearchAgent and DeepSearchAgent, work directly with prose
  - **Integration Note**: Leverage existing agent quality and proven capabilities
  - **Deliverables**:
    - ✅ Created `underlines_adk/agents/research_planner.py` with flexible ResearchPlanner LlmAgent
    - ✅ Added comprehensive test script `examples/test_research_planner.py` validating 4 newsletter types
    - ✅ Validated flexibility requirements: daily news, biotech VC, fintech, academic research
    - ✅ All existing tests continue to pass (17/17)
    - ✅ Updated agents module to include research_planner
  - **Commit**: `feat: implement research planning orchestrator for existing agents`

#### Day 2: Dynamic Research Coordination Using Existing Agents ✅ COMPLETED

- [x] **Implement DynamicResearchCoordinator as Agent Orchestrator**
  - [x] Create custom BaseAgent that uses LLM intelligence to orchestrate existing agents
  - [x] Implement LLM-driven coordination strategy instead of programmatic logic
  - [x] Remove all hardcoded keyword matching and programmatic decision trees
  - [x] Create workflows that use LLM understanding to coordinate WideSearchAgent and DeepSearchAgent
  - [x] Test LLM-driven coordination follows agentic principles (no programmatic validation)
  - [x] Validate coordination adapts to ANY newsletter type using LLM intelligence
  - [x] Ensure no hardcoded section names or structures in orchestration logic
  - [x] Ensure tests pass and update TODOS.md
  - **Acceptance Criteria**: ✅ LLM intelligence coordinates existing agents without programmatic logic
  - **Dependencies**: Day 1 completion
  - **Key Pattern**: LLM-driven coordination using agentic principles, trust LLM intelligence over programmatic logic
  - **Integration Note**: Uses LLM intelligence to coordinate existing WideSearchAgent and DeepSearchAgent capabilities
  - **Deliverables**:
    - ✅ Created `underlines_adk/agents/dynamic_research_coordinator.py` with LLM-driven coordination
    - ✅ Removed all hardcoded programmatic logic (keyword matching, decision trees)
    - ✅ Added proper error handling for missing critical information (user requirements, research plan)
    - ✅ Added comprehensive unit test suite `tests/unit/test_dynamic_research_coordinator.py` (10 tests)
    - ✅ Validated agentic principles: LLM intelligence over programmatic validation
    - ✅ All tests continue to pass (32/32)
    - ✅ Updated agents module to include dynamic_research_coordinator
  - **Commit**: `feat: implement LLM-driven research coordination using agentic principles`

#### Day 3: Quality Assessment Wrapper for Existing Agent Outputs

- [x] **Implement Agentic Quality Assessment Layer Over Existing Agents** ✅ COMPLETED
  - [x] Create QualityAssessmentAgent (`underlines_adk/agents/quality_assessment_agent.py`) with ADK-native BaseAgent pattern
  - [x] Use `Event(actions=EventActions(escalate=True))` pattern for loop termination (NOT tool-based approach)
  - [x] Implement LoopAgent wrapper that coordinates existing WideSearchAgent and DeepSearchAgent with agentic quality assessment
  - [x] Trust LLM intelligence completely - no hardcoded dictionary return structures
  - [x] Add quality assessment layer that evaluates outputs using LLM judgment in natural language
  - [x] Test agentic escalation against diverse quality standards using existing agent outputs
  - [x] Ensure explicit error handling with clear ValueError messages for missing inputs
  - [x] **CRITICAL FIX**: Implement context propagation to existing agents
    - [x] Create context-aware agent wrappers that inject user requirements and research plan
    - [x] Update existing WideSearchAgent and DeepSearchAgent to be context-aware
    - [x] Ensure proper session state management for context sharing between agents
    - [x] Fix research result accumulation in QualityAssessmentAgent
  - [ ] Ensure quality assessment adapts to user's stated requirements (NOT hardcoded criteria)
  - [ ] Validate quality assessment works with WideSearchAgent and DeepSearchAgent output formats
  - [ ] Test against biotech VC, fintech, and academic newsletter requirements using existing agents
  - [ ] Update tool and agent documentation with new patterns
  - [ ] Ensure tests pass and update TODOS.md
  - **Acceptance Criteria**: Quality assessment layer enhances existing agent outputs using ADK-native patterns
  - **Dependencies**: Day 2 completion
  - **Key Pattern**: LoopAgent wrapper around existing agents, LLM judgment with EventActions escalation
  - **Integration Note**: Quality enhancement without modifying existing agent logic
  - **Implementation Plan**: See `.tasks/backlog/day3_quality_assessment_implementation_plan.md`
  - **Commit**: `feat: implement quality assessment wrapper for existing agents`

#### Day 4: End-to-End Pipeline Integration with Existing Agents

- [ ] **Complete Flexible Pipeline Using Existing Agent Orchestration**
  - [ ] Integrate ResearchPlanner → DynamicResearchCoordinator → FlexibleNewsletterSynthesizer
  - [ ] Implement FlexibleNewsletterSynthesizer that reads outputs from existing WideSearchAgent and DeepSearchAgent
  - [ ] Create synthesis logic that formats research from existing agents according to user specifications
  - [ ] Test complete pipeline with all three example newsletter types using existing agent capabilities
  - [ ] Validate that existing agents provide sufficient research quality for any newsletter type
  - [ ] Validate output format adaptation based on user requirements using existing agent research
  - [ ] Ensure NO hardcoded newsletter-specific logic anywhere in orchestration pipeline
  - [ ] Ensure tests pass and update TODOS.md
  - **Acceptance Criteria**: Pipeline orchestrating existing agents generates different newsletter formats
  - **Dependencies**: Day 3 completion
  - **Key Pattern**: SequentialAgent pipeline orchestrating existing WideSearchAgent and DeepSearchAgent
  - **Integration Note**: Leverage existing agent quality while adding flexible orchestration
  - **Commit**: `feat: complete flexible newsletter pipeline orchestrating existing agents`

#### Day 5: Pipeline Validation and Integration Documentation

- [ ] **Validate Flexibility and Document Integration Strategy**
  - [ ] Test pipeline with additional newsletter types beyond the three examples using existing agents
  - [ ] Document natural language input patterns and expected outputs from existing agent orchestration
  - [ ] Create usage examples showing how existing agents serve different user types
  - [ ] Validate against all flexibility requirements in `.docs/PIPELINE_FLEXIBILITY_REQUIREMENTS.md`
  - [ ] Ensure orchestration instructions pass universal newsletter type validation checklist
  - [ ] Document integration strategy and existing agent leverage in `.docs/`
  - [ ] Ensure tests pass and update TODOS.md
  - **Acceptance Criteria**: Pipeline demonstrates universal adaptability using existing agent capabilities
  - **Dependencies**: Day 4 completion
  - **Key Pattern**: Comprehensive validation of existing agent orchestration flexibility
  - **Integration Note**: Document how existing assets enable universal newsletter generation
  - **Commit**: `docs: add flexible newsletter pipeline integration examples`

### Step 2: Pipeline Enhancement and Optimization (3 days)

#### Day 1: Advanced Research Capabilities

- [ ] **Enhance Research Agent Capabilities**
  - [ ] Implement advanced citation validation for any newsletter type
  - [ ] Add source diversity analysis that adapts to user requirements
  - [ ] Create perspective analysis tools that work with any topic domain
  - [ ] Ensure all enhancements maintain universal flexibility
  - **Acceptance Criteria**: Enhanced research capabilities work across all newsletter types
  - **Dependencies**: Step 1 completion
  - **Key Pattern**: Enhancements must be generic, not newsletter-type-specific

#### Day 2: Resource Management and Performance

- [ ] **Implement Universal Resource Controls**
  - [ ] Add API rate limiting and quota management for any newsletter type
  - [ ] Implement graceful degradation under load
  - [ ] Add monitoring and metrics collection
  - [ ] Configure resource allocation that scales with user requirements
  - **Acceptance Criteria**: System handles concurrent load for any newsletter type
  - **Dependencies**: Day 1 completion
  - **Key Pattern**: Resource management adapts to user requirements, not fixed limits

#### Day 3: Performance Testing and Validation

- [ ] **Comprehensive Performance Testing**
  - [ ] Load testing with multiple newsletter types simultaneously
  - [ ] API usage and cost optimization validation across different use cases
  - [ ] Memory usage and performance profiling for diverse requirements
  - [ ] Integration testing with various natural language inputs
  - **Acceptance Criteria**: System performs efficiently for any newsletter type
  - **Dependencies**: Day 2 completion
  - **Key Pattern**: Performance validation across universal use cases

---

## Phase 2: Advanced Features & User Experience (Q2 2025)

### Step 3: Advanced Natural Language Processing (3 days)

- [ ] **Enhanced Natural Language Understanding**
  - [ ] Implement advanced requirement parsing for complex newsletter specifications
  - [ ] Add support for multi-language newsletter requirements
  - [ ] Create intelligent requirement clarification when user input is ambiguous
  - [ ] Ensure all enhancements maintain universal flexibility
  - **Dependencies**: Phase 1 completion
  - **Key Pattern**: Enhanced understanding must work for ANY newsletter type

### Step 4: Output Format Flexibility (3 days)

- [ ] **Universal Output Generation**
  - [ ] Implement multiple output formats (Markdown, HTML, PDF) based on user requirements
  - [ ] Add email delivery integration that adapts to any newsletter type
  - [ ] Create newsletter archival system for any format
  - [ ] Implement version control for newsletters of any type
  - **Dependencies**: Step 3 completion
  - **Key Pattern**: Output generation adapts to user specifications, not predefined formats

### Step 5: Quality Enhancement Tools (2 days)

- [ ] **Advanced Quality Assessment**
  - [ ] Implement sophisticated citation validation for any domain
  - [ ] Add bias detection that works across all newsletter types
  - [ ] Create fact-checking tools that adapt to different subject areas
  - [ ] Ensure quality tools maintain universal applicability
  - **Dependencies**: Step 4 completion
  - **Key Pattern**: Quality tools must be domain-agnostic and user-requirement-driven

---

## Phase 3: User Interface & Experience (Q3 2025)

### Step 7: Web Interface (5-7 days)

- [ ] **User Interface Development**
  - [ ] Create web interface for manifest creation
  - [ ] Implement newsletter preview and editing
  - [ ] Add user feedback and rating system
  - [ ] Create dashboard for newsletter management

### Step 8: Personalization Engine (3-4 days)

- [ ] **Advanced Personalization**
  - [ ] Implement user preference learning
  - [ ] Add content recommendation algorithms
  - [ ] Create adaptive quality thresholds
  - [ ] Implement usage analytics

---

## Phase 4: Scale & Enterprise Features (Q4 2025)

### Step 9: Enterprise Features (4-5 days)

- [ ] **Business Features**
  - [ ] Multi-user organization support
  - [ ] API access for enterprise customers
  - [ ] Advanced analytics and reporting
  - [ ] Custom branding and white-labeling

### Step 10: Advanced Intelligence (3-4 days)

- [ ] **Enhanced AI Capabilities**
  - [ ] Multi-language support
  - [ ] Advanced fact-checking algorithms
  - [ ] Predictive content recommendations
  - [ ] Automated quality improvement

---

## Development Guidelines

### 🚨 CRITICAL: Pipeline Flexibility Validation

**BEFORE IMPLEMENTING ANY AGENT OR WORKFLOW**, validate against the flexibility requirements in [BUILDING_ORCHESTRATORS.md](../.docs/BUILDING_ORCHESTRATORS.md).

### Daily Workflow

1. **Start of Day**: Review current task and flexibility requirements
2. **Implementation**: Focus on single task with universal applicability
3. **Flexibility Validation**: Test against multiple newsletter types
4. **Testing**: Ensure all tests pass before moving to next task
5. **Documentation**: Update relevant documentation
6. **End of Day**: Update TODOS.md with progress and commit changes

### Quality Standards

- All new code must have comprehensive tests across multiple newsletter types
- Documentation must be updated for any new features
- Performance impact must be assessed and optimized for any use case
- Security considerations must be addressed universally
- **CRITICAL**: All agent instructions must pass flexibility validation checklist

### Commit Strategy

- One commit per completed task using conventional commits format
- Clear commit messages referencing task completion
- All tests must pass before committing
- Update TODOS.md in each commit
- **Format**: `feat: implement [feature]` or `docs: add [documentation]`

---

## Risk Mitigation

See [PRD.md](../.docs/PRD.md) for product constraints and [TROUBLESHOOTING.md](../.docs/TROUBLESHOOTING.md) for technical risk mitigation.

---

*This roadmap is a living document that should be updated daily with progress and any changes in priorities or requirements.*
