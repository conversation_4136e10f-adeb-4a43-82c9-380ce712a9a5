# Underlines ADK - AI Newsletter Generation System

An intelligent newsletter generation platform built on Google ADK that transforms natural language requirements into comprehensive, well-researched newsletters.

## What This System Does

Transform natural language descriptions like:

- *"Weekly tech newsletter with AI developments and business implications"*
- *"Daily biotech updates focusing on TL1A research and clinical trials"*
- *"Monthly fintech regulatory updates with competitive analysis"*

Into professional-quality newsletters with proper research, citations, and analysis.

## Key Features

- **Universal Flexibility**: Generate any type of newsletter from natural language descriptions
- **Professional Quality**: Research depth and citation standards that match human experts
- **Existing Agent Integration**: Leverages proven WideSearchAgent and DeepSearchAgent through orchestration
- **ADK-Native Architecture**: Built using Google ADK patterns (SequentialAgent, ParallelAgent, LoopAgent)

## Architecture Overview

The system uses an **orchestration strategy** that coordinates existing high-quality agents rather than rebuilding research capabilities:

```text
User Requirements (Natural Language)
           ↓
    ResearchPlanner
           ↓
 DynamicResearchCoordinator
    ↓              ↓
WideSearchAgent  DeepSearchAgent  (existing agents)
    ↓              ↓
   NewsletterSynthesizer
           ↓
    Final Newsletter
```

## Quick Start

### For New Developers

1. **Get Running**: [SETUP.md](.docs/SETUP.md) - Environment setup and first agent run
2. **Understand Assets**: [EXISTING_AGENTS.md](.docs/EXISTING_AGENTS.md) - Learn about WideSearchAgent and DeepSearchAgent
3. **🚨 CRITICAL**: [ANTI_PATTERNS_AND_VALIDATION.md](.docs/ANTI_PATTERNS_AND_VALIDATION.md) - **READ THIS FIRST** to avoid common mistakes
4. **Start Building**: [BUILDING_ORCHESTRATORS.md](.docs/BUILDING_ORCHESTRATORS.md) - Create coordination layers

### For Product Understanding

- **Product Vision**: [PRD.md](.docs/PRD.md) - Non-technical product requirements and user needs

### For Troubleshooting

- **Fix Issues**: [TROUBLESHOOTING.md](.docs/TROUBLESHOOTING.md) - Debug, test, and deploy

## Core Principles

1. **Orchestration Over Duplication**: Use existing WideSearchAgent and DeepSearchAgent through coordination layers
2. **Universal Flexibility**: Every component works for any newsletter type (daily news, biotech VC, fintech, academic)
3. **Natural Language Requirements**: Work directly with user prose, no structured data extraction
4. **LLM-Driven Quality**: Use tool-based escalation and LLM judgment, not programmatic validation

## Target Users

- **General News Readers**: Personalized news digests and topic summaries
- **Industry Professionals**: Biotech VCs tracking research and companies
- **Business Teams**: Fintech startups needing regulatory and competitive intelligence

## Technology Stack

- **Google ADK**: Agent orchestration and workflow management
- **Exa API**: Web search and content discovery
- **Google Gemini**: LLM for analysis and synthesis
- **Python 3.13+**: Implementation language
- **Poetry**: Dependency management

## Project Status

**Current Phase**: Building orchestration layers around existing agents

- ✅ WideSearchAgent and DeepSearchAgent (proven, high-quality research capabilities)
- 🔄 ResearchPlanner, DynamicResearchCoordinator, NewsletterSynthesizer (in development)
- 📋 Quality assessment and testing frameworks

See [TODOS.md](.tasks/TODOS.md) for current development roadmap.

## Documentation Structure

- **[PRD.md](.docs/PRD.md)** - Product vision and requirements (non-technical)
- **[SETUP.md](.docs/SETUP.md)** - Environment setup and first agent run
- **[EXISTING_AGENTS.md](.docs/EXISTING_AGENTS.md)** - WideSearchAgent & DeepSearchAgent capabilities
- **🚨 [ANTI_PATTERNS_AND_VALIDATION.md](.docs/ANTI_PATTERNS_AND_VALIDATION.md)** - **CRITICAL**: Anti-patterns to avoid and validation checklists
- **[BUILDING_ORCHESTRATORS.md](.docs/BUILDING_ORCHESTRATORS.md)** - How to build coordination layers
- **[TROUBLESHOOTING.md](.docs/TROUBLESHOOTING.md)** - Debugging, testing, deployment

## Contributing

1. Read [PRD.md](.docs/PRD.md) to understand the product vision
2. Follow [SETUP.md](.docs/SETUP.md) to get your environment running
3. Study [EXISTING_AGENTS.md](.docs/EXISTING_AGENTS.md) to understand what already exists
4. **🚨 CRITICAL**: Read [ANTI_PATTERNS_AND_VALIDATION.md](.docs/ANTI_PATTERNS_AND_VALIDATION.md) to avoid common mistakes
5. Build orchestration layers following [BUILDING_ORCHESTRATORS.md](.docs/BUILDING_ORCHESTRATORS.md)
6. Test and debug using [TROUBLESHOOTING.md](.docs/TROUBLESHOOTING.md)

## License

[Add your license information here]

## Contact

[Add contact information here]
