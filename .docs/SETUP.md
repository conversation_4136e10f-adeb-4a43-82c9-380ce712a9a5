# Setup Guide

## What This Document Is For

Get the Underlines ADK newsletter generation system running on your machine and verify that existing agents work correctly.

## What You'll Accomplish

- Install all dependencies and configure your development environment
- Set up API keys and environment variables
- Run your first agent to verify everything works
- Understand the basic project structure

## Prerequisites

- Python 3.13+
- Git
- API keys for Exa and Google services
- Basic familiarity with Python and command line

## Installation Steps

### 1. <PERSON>lone and Setup Repository

```bash
# Clone repository
git clone https://github.com/dtedesco1/underlines-adk.git
cd underlines-adk

# Install dependencies using Poetry
poetry install

# Verify Poetry installation worked
poetry run python -c "import google.adk; print('Google ADK installed successfully')"
```

### 2. Configure Environment Variables

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your API keys
# Required API Keys:
EXA_API_KEY=your_exa_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Optional Configuration (defaults shown):
MAX_FACT_CHECK_ITERATIONS=3
MAX_PARALLEL_TOPICS=5
MAX_QUALITY_ASSESSMENT_TOKENS=1000
ADK_SESSION_LOG_LEVEL=INFO
ADK_AGENT_TIMEOUT=300
```

### 3. Get Required API Keys

#### Exa API Key

1. Visit [Exa API](https://exa.ai) and create an account
2. Generate an API key from your dashboard
3. Add to `.env` file as `EXA_API_KEY=your_key_here`

#### Google API Key

1. Visit [Google AI Studio](https://aistudio.google.com)
2. Create a new API key for Gemini
3. Add to `.env` file as `GOOGLE_API_KEY=your_key_here`

## Verify Installation

### Test Basic Agent Functionality

```bash
# Run a simple test to verify WideSearchAgent works
poetry run python -c "
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent
from underlines_adk.sessions import LoggedSessionService
from google.adk.runners import Runner
from google.genai import types
import asyncio

async def test_agent():
    session_service = LoggedSessionService()
    runner = Runner(
        agent=wide_search_agent,
        app_name='test_app',
        session_service=session_service
    )

    session = await session_service.create_session(
        app_name='test_app',
        user_id='test_user'
    )

    user_content = types.Content(
        parts=[types.Part(text='{\"query\": \"AI news\", \"lookback_days\": 7}')]
    )

    print('Testing WideSearchAgent...')
    async for event in runner.run_async(
        user_id='test_user',
        session_id=session.id,
        new_message=user_content
    ):
        if event.is_final_response():
            print('✅ WideSearchAgent working correctly!')
            print('Sample result:', event.content.parts[0].text[:200] + '...')
            break

asyncio.run(test_agent())
"
```

### Expected Output

You should see:

```
Testing WideSearchAgent...
✅ WideSearchAgent working correctly!
Sample result: [{"title": "AI breakthrough in...", "reference_urls": [...], ...
```

### Test Error Handling (MANDATORY)

Verify that your agents properly handle missing inputs:

```bash
# Test error handling with missing inputs
poetry run python -c "
from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator
from unittest.mock import MagicMock

# Test that DynamicResearchCoordinator raises proper errors for missing inputs
coordinator = DynamicResearchCoordinator()
mock_ctx = MagicMock()
mock_ctx.session.state = {}  # Empty state - should trigger errors

try:
    import asyncio
    async def test_error_handling():
        async for _ in coordinator._run_async_impl(mock_ctx):
            pass
    asyncio.run(test_error_handling())
    print('❌ ERROR: DynamicResearchCoordinator should have raised ValueError for missing inputs!')
except ValueError as e:
    print('✅ Error handling working correctly:', str(e))
except Exception as e:
    print('❌ Unexpected error type:', type(e), str(e))
"
```

### Expected Error Handling Output

You should see:

```
✅ Error handling working correctly: No user requirements found for research coordination
```

## Project Structure Overview

```
underlines-adk/
├── .docs/                          # Documentation (you are here)
├── .tasks/                         # Project management and TODOs
│   ├── TODOS.md                    # Current development roadmap
│   ├── backlog/                    # Future task planning
│   └── complete/                   # Completed task archive
├── examples/                       # Usage examples and testing scripts
│   ├── run_deep_search.py          # DeepSearchAgent example
│   ├── run_wide_search.py          # WideSearchAgent example
│   ├── test_dynamic_research_coordinator.py  # Coordinator testing
│   └── test_research_planner.py    # Research planning example
├── underlines_adk/                 # Main Python package
│   ├── agents/                     # AI agents for search and coordination
│   │   ├── wide_search_agent.py    # WideSearchAgent - broad web search coverage
│   │   ├── deep_search_agent.py    # DeepSearchAgent - in-depth analysis with citations
│   │   ├── research_planner.py     # ResearchPlanner - research workflow orchestration
│   │   └── dynamic_research_coordinator.py  # DynamicResearchCoordinator - LLM-driven coordination
│   ├── tools/                      # Tool implementations (Exa API, LiteLLM)
│   ├── sessions/                   # Session management with logging
│   ├── logging/                    # Structured logging utilities
│   └── callbacks/                  # Event logging and monitoring
├── tests/                          # Test suite
│   ├── unit/                       # Unit tests for individual components
│   └── integration/                # End-to-end integration tests
├── session_logs/                   # Agent execution logs (created automatically)
└── pyproject.toml                  # Dependencies and project configuration
```

## Common Setup Issues

### Python Version Issues

```bash
# Check Python version
python --version  # Should be 3.13+

# If using pyenv to manage Python versions
pyenv install 3.13.0
pyenv local 3.13.0
```

### Poetry Installation Issues

```bash
# Install Poetry if not already installed
curl -sSL https://install.python-poetry.org | python3 -

# Verify Poetry installation
poetry --version
```

### API Key Issues

- **Exa API**: Make sure you have credits in your Exa account
- **Google API**: Ensure the API key has access to Gemini models
- **Environment Variables**: Verify `.env` file is in the project root and properly formatted

### Import Errors

```bash
# If you get import errors, try reinstalling dependencies
poetry install --no-cache

# Verify Google ADK is properly installed
poetry run python -c "from google.adk.agents import LlmAgent; print('ADK working')"
```

## Development Environment Setup

### Recommended IDE Configuration

- **VS Code**: Install Python extension and configure to use Poetry virtual environment
- **PyCharm**: Configure interpreter to use Poetry virtual environment
- **Vim/Neovim**: Install Python LSP and configure for Poetry

### Enable Debug Logging (Optional)

```bash
# Add to .env for more verbose logging during development
ADK_SESSION_LOG_LEVEL=DEBUG
DEVELOPMENT_MODE=true
```

## Next Steps

Once your environment is working:

1. **Understand existing capabilities**: Read [EXISTING_AGENTS.md](EXISTING_AGENTS.md) to learn about WideSearchAgent, DeepSearchAgent, ResearchPlanner, and DynamicResearchCoordinator
2. **Start building**: Follow [BUILDING_ORCHESTRATORS.md](BUILDING_ORCHESTRATORS.md) to create coordination layers
3. **Get help**: Consult [TROUBLESHOOTING.md](TROUBLESHOOTING.md) if you encounter issues

## Quick Reference

- **Run tests**: `poetry run pytest`
- **Check logs**: Look in `session_logs/` directory
- **Update dependencies**: `poetry update`
- **Activate shell**: `poetry shell`
