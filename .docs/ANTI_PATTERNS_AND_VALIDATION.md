# Anti-Patterns and Validation Guide

## What This Document Is For

Prevent future developers from implementing anti-patterns that violate our core agentic principles. This document provides comprehensive validation checklists and examples of what NOT to do.

## What You'll Accomplish

- Understand the specific anti-patterns that MUST be avoided
- Learn validation checklists to run before any implementation
- See concrete examples of violations and their correct alternatives
- Implement proper error handling and context propagation patterns

## Prerequisites

- Read [PRD.md](PRD.md) to understand agentic principles
- Complete [EXISTING_AGENTS.md](EXISTING_AGENTS.md) to understand orchestration approach
- Review [BUILDING_ORCHESTRATORS.md](BUILDING_ORCHESTRATORS.md) for implementation patterns

## 🚨 CRITICAL ANTI-PATTERNS THAT MUST BE AVOIDED

### ANTI-PATTERN 1: Hardcoded Dictionary Return Structures

**Problem**: Tools/functions that return predetermined dictionary structures violate agentic principles by not trusting LLM intelligence.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Hardcoded quality assessment
def assess_quality(research_results: str) -> dict:
    return {
        "status": "sufficient",      # Hardcoded keys
        "quality_level": "high",     # Predetermined structure
        "score": 8.5,               # Numerical scoring
        "meets_standards": True,     # Boolean flags
        "recommendations": [...]     # Fixed schema
    }

# ❌ WRONG - Structured parsing of LLM responses
def parse_llm_assessment(llm_response: str) -> dict:
    # Trying to extract structured data from natural language
    if "sufficient" in llm_response.lower():
        return {"status": "pass", "quality": "high"}
    else:
        return {"status": "fail", "quality": "low"}
```

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - Trust LLM intelligence completely
async def assess_quality_agentic(user_requirements: str, research_results: str) -> str:
    prompt = f"""
    Evaluate this research against user requirements:
    
    Requirements: {user_requirements}
    Research: {research_results}
    
    Respond with "SUFFICIENT" or "NEEDS_IMPROVEMENT" followed by your reasoning.
    """
    
    llm_response = await llm.agenerate([{"role": "user", "content": prompt}])
    return llm_response.generations[0][0].text  # Return raw LLM assessment

# ✅ CORRECT - Simple pass/fail determination
def is_quality_sufficient(llm_assessment: str) -> bool:
    return llm_assessment.upper().startswith("SUFFICIENT")
```

### ANTI-PATTERN 2: Context Isolation

**Problem**: Agents that ignore session state context instead of properly propagating user requirements and research plans.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Agent operates in isolation
class IsolatedQualityAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        # Ignores user_requirements from session state
        generic_assessment = "Research looks good"
        return generic_assessment

# ❌ WRONG - Using fallback values instead of validation
class SilentFallbackAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        # Silent fallback masks missing context
        user_requirements = ctx.session.state.get("user_requirements", "default requirements")
        research_plan = ctx.session.state.get("research_plan", "generic plan")
```

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - Context-aware agent with explicit validation
class ContextAwareQualityAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        # MANDATORY: Validate required context first
        user_requirements = ctx.session.state.get("user_requirements", "")
        research_results = ctx.session.state.get("research_results", "")
        
        # MANDATORY: Explicit error handling for missing inputs
        if not user_requirements:
            raise ValueError("No user requirements found for quality assessment")
        if not research_results:
            raise ValueError("No research results found for quality assessment")
        
        # Use context in assessment logic
        assessment = await self.evaluate_against_requirements(user_requirements, research_results)
        
        yield Event(actions=EventActions(
            state_delta={"quality_assessment": assessment},
            escalate=self.is_sufficient(assessment)
        ))
```

### ANTI-PATTERN 3: Agent Duplication

**Problem**: Creating new agents that duplicate existing functionality instead of orchestrating proven agents.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Newsletter-specific agents duplicate functionality
class DailyNewsAgent(LlmAgent):
    def __init__(self):
        super().__init__(
            instruction="Research daily news topics...",  # Duplicates WideSearchAgent
            tools=[exa_wide_search]
        )

class BiotechVCAgent(LlmAgent):
    def __init__(self):
        super().__init__(
            instruction="Research biotech companies...",  # Duplicates DeepSearchAgent
            tools=[exa_deep_search]
        )

# ❌ WRONG - Modifying existing agents
wide_search_agent.instruction = "Focus only on technology..."  # Breaks backward compatibility
```

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - Orchestrate existing agents with context injection
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent
from underlines_adk.agents.deep_search_agent import exa_agent as deep_search_agent

class ContextAwareAgentWrapper(BaseAgent):
    def __init__(self, wrapped_agent, user_requirements, research_plan):
        super().__init__(name=f"ContextAware{wrapped_agent.name}")
        self.wrapped_agent = wrapped_agent
        self.user_requirements = user_requirements
        self.research_plan = research_plan

    async def _run_async_impl(self, ctx):
        # Inject context into session state
        yield Event(actions=EventActions(state_delta={
            "user_requirements": self.user_requirements,
            "research_plan": self.research_plan
        }))
        
        # Run existing agent with proper context
        async for event in self.wrapped_agent.run_async(ctx):
            yield event

# Use through orchestration
research_coordinator = ParallelAgent(sub_agents=[
    ContextAwareAgentWrapper(wide_search_agent, requirements, plan),
    ContextAwareAgentWrapper(deep_search_agent, requirements, plan)
])
```

### ANTI-PATTERN 4: Silent Error Handling

**Problem**: Using fallback values or silent error handling instead of explicit validation and clear error messages.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Silent fallbacks mask problems
def get_user_requirements_with_fallback(ctx):
    return ctx.session.state.get("user_requirements", "default requirements")

# ❌ WRONG - Generic error handling
try:
    result = some_operation()
except Exception:
    return "something went wrong"  # Unhelpful error message

# ❌ WRONG - Continuing with incomplete data
if not user_requirements:
    user_requirements = "generate a newsletter"  # Fallback value
```

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - Explicit validation with clear errors
def get_required_state(ctx, key: str, error_message: str = None) -> str:
    value = ctx.session.state.get(key, "")
    if not value:
        error_msg = error_message or f"Required state key '{key}' not found"
        raise ValueError(error_msg)
    return value

# ✅ CORRECT - Specific error handling
async def _run_async_impl(self, ctx):
    try:
        user_requirements = get_required_state(
            ctx, "user_requirements", 
            "No user requirements found for research coordination"
        )
        research_plan = get_required_state(
            ctx, "research_plan",
            "No research plan found for research coordination"
        )
    except ValueError as e:
        # Re-raise with context about which agent failed
        raise ValueError(f"{self.name}: {str(e)}")
```

## 🚨 MANDATORY VALIDATION CHECKLISTS

### Pre-Implementation Validation

**Run this checklist BEFORE writing ANY code:**

- [ ] **Agentic Intelligence**: Does this trust LLM judgment over programmatic logic?
- [ ] **Context Propagation**: Does this read and validate session state properly?
- [ ] **Orchestration**: Does this use existing agents instead of duplicating functionality?
- [ ] **Error Handling**: Does this raise explicit errors instead of silent fallbacks?
- [ ] **Universal Flexibility**: Does this work for ANY newsletter type without modification?

### Tool/Function Implementation Validation

**For ANY tool or function:**

- [ ] **No Hardcoded Returns**: Avoids predetermined dictionary structures?
- [ ] **LLM Intelligence**: Returns raw LLM responses instead of parsed data?
- [ ] **Natural Language**: Works with natural language input/output?
- [ ] **Clear Errors**: Raises ValueError for missing required inputs?

### Agent Implementation Validation

**For ANY agent:**

- [ ] **Context Awareness**: Reads user_requirements and research_plan from session state?
- [ ] **Explicit Validation**: Raises ValueError for missing required inputs?
- [ ] **No Duplication**: Orchestrates existing agents instead of rebuilding functionality?
- [ ] **Agentic Principles**: Trusts LLM intelligence over programmatic decision trees?

### Quality Assessment Validation

**For ANY quality assessment:**

- [ ] **LLM-Driven**: Uses LLM judgment instead of numerical scoring?
- [ ] **Natural Language**: Returns LLM's raw assessment without parsing?
- [ ] **User-Specific**: Evaluates against user's stated requirements?
- [ ] **No Fixed Criteria**: Avoids hardcoded quality standards?

## Context Propagation Requirements

### Mandatory Session State Management

**ALL agents MUST follow these patterns:**

```python
# MANDATORY: Read required context with validation
user_requirements = ctx.session.state.get("user_requirements", "")
research_plan = ctx.session.state.get("research_plan", "")

# MANDATORY: Explicit error handling for missing context
if not user_requirements:
    raise ValueError("No user requirements found for [agent_name]")
if not research_plan:
    raise ValueError("No research plan found for [agent_name]")
```

### Context Injection Pattern

**For orchestrating existing agents:**

```python
# Inject context before running existing agents
yield Event(actions=EventActions(state_delta={
    "user_requirements": self.user_requirements,
    "research_plan": self.research_plan,
    "current_focus": self.focus_area
}))

# Then run existing agent with proper context
async for event in existing_agent.run_async(ctx):
    yield event
```

## Implementation Validation Process

### Step 1: Design Review

Before writing code, validate your design:

1. Does this approach trust LLM intelligence?
2. Does this orchestrate existing agents?
3. Does this handle context properly?
4. Does this raise explicit errors?

### Step 2: Code Review

Before committing code, validate implementation:

1. Run all validation checklists above
2. Test with missing inputs (should raise clear errors)
3. Test with different newsletter types (should work universally)
4. Verify no hardcoded return structures

### Step 3: Integration Testing

Before deployment:

1. Test context propagation through full pipeline
2. Verify existing agents still work (backward compatibility)
3. Test error handling with real missing data scenarios
4. Validate LLM assessment quality across newsletter types

## Next Steps

- Apply these validation checklists to ALL new implementations
- Review existing code for anti-pattern violations
- Update any code that violates these principles
- Consult [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for debugging guidance

## Key Principle

**Trust LLM intelligence, orchestrate existing agents, propagate context explicitly, and fail fast with clear errors.**
