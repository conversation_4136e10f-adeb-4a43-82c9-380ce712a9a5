"""
Dynamic Research Coordinator for orchestrating existing agents using LLM intelligence.

This module provides the DynamicResearchCoordinator, a custom BaseAgent that uses
LLM intelligence to orchestrate existing WideSearchAgent and DeepSearchAgent instances.
It follows agentic principles by trusting LLM judgment instead of programmatic logic
to determine research coordination strategies.

Key Features:
    - LLM-driven research coordination decisions (no hardcoded logic)
    - Universal flexibility for any newsletter type
    - Orchestrates existing WideSearchAgent and DeepSearchAgent instances
    - Uses ParallelAgent patterns for concurrent research
    - Trusts LLM intelligence over programmatic validation

Agentic Principles:
    - Uses LLM to analyze requirements and determine research approach
    - No hardcoded keyword matching or programmatic decision trees
    - Adapts to ANY newsletter type through LLM understanding
    - Leverages existing agent capabilities without duplication

Usage:
    from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator
    from google.adk.runners import Runner

    coordinator = DynamicResearchCoordinator(
        name="FlexibleResearchCoordinator",
        description="Coordinates research using existing agents"
    )

    runner = Runner(agent=coordinator, ...)

Dependencies:
    - google.adk.agents: BaseAgent, ParallelAgent, LlmAgent
    - google.adk.events: Event
    - google.genai: types
    - ..tools.exa_tools: exa_wide_search, exa_deep_search
    - ..tools.litellm_tools: llm
"""

import os
from typing import AsyncGenerator
from google.adk.agents import BaseAgent, ParallelAgent, LoopAgent
from google.adk.events import Event, EventActions

from .wide_search_agent import exa_agent as wide_search_agent
from .deep_search_agent import exa_agent as deep_search_agent
from .quality_assessment_agent import QualityAssessmentAgent


class DynamicResearchCoordinator(BaseAgent):
    """
    Custom BaseAgent that uses LLM intelligence to orchestrate existing agents.

    This coordinator uses LLM judgment to analyze user requirements and determine
    the best research coordination approach. It follows agentic principles by
    trusting LLM intelligence instead of hardcoded programmatic logic.

    The coordinator creates research workflows dynamically based on LLM analysis
    of user requirements, maintaining universal flexibility for any newsletter type.
    """

    def __init__(self, name: str = "DynamicResearchCoordinator", description: str | None = None):
        """Initialize the DynamicResearchCoordinator."""
        if description is None:
            description = "Uses LLM intelligence to coordinate research using existing agents"

        super().__init__(
            name=name,
            description=description,
            sub_agents=[],  # We'll create sub-agents dynamically using LLM intelligence
            before_agent_callback=None,
            after_agent_callback=None
        )

    async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
        """
        Implement the core orchestration logic for dynamic research coordination.

        This method:
        1. Reads user requirements and research plan from session state
        2. Analyzes requirements to determine research approach
        3. Creates dynamic ParallelAgent workflows using existing agents
        4. Coordinates concurrent research execution
        5. Yields events from the parallel research workflows
        """
        # Read user requirements and research plan from session state
        user_requirements = ctx.session.state.get("user_requirements", "")
        research_plan = ctx.session.state.get("research_plan", "")

        # If no user requirements in state, raise an error
        if not user_requirements:
            raise ValueError("No user requirements found for research coordination")
    
        # If no research plan in state, raise an error
        if not research_plan:
            raise ValueError("No research plan found for research coordination")

        # Always proceed with coordination - the LLM will adapt to whatever requirements are provided
        
        # Use LLM intelligence to create research coordination strategy
        coordination_strategy = await self._create_llm_driven_coordination_strategy(
            user_requirements, research_plan, ctx
        )

        # Execute the LLM-determined coordination strategy
        async for event in coordination_strategy.run_async(ctx):
            yield event

    async def _create_llm_driven_coordination_strategy(self, user_requirements: str, research_plan: str, ctx) -> BaseAgent:
        """
        Create context-aware coordination strategy using existing agents.

        This method creates context-aware wrappers around the existing WideSearchAgent
        and DeepSearchAgent that inject user requirements and research plan into their
        execution context, following the principle of "Orchestrate, don't duplicate."

        Args:
            user_requirements: Natural language user requirements
            research_plan: Research plan from ResearchPlanner
            ctx: ADK context for session state access

        Returns:
            BaseAgent that orchestrates existing proven agents with proper context
        """
        # Create context-aware wrappers that inject user requirements into existing agents
        context_aware_wide_search = self._create_context_aware_agent(
            base_agent=wide_search_agent,
            agent_name="ContextAwareWideSearch",
            user_requirements=user_requirements,
            research_plan=research_plan,
            focus="broad coverage and comprehensive topic discovery"
        )

        context_aware_deep_search = self._create_context_aware_agent(
            base_agent=deep_search_agent,
            agent_name="ContextAwareDeepSearch",
            user_requirements=user_requirements,
            research_plan=research_plan,
            focus="detailed analysis and in-depth investigation"
        )

        # ParallelAgent coordinates both context-aware agents simultaneously
        research_coordinator = ParallelAgent(
            name="ContextAwareAgentCoordination",
            sub_agents=[
                context_aware_wide_search,  # Context-aware WideSearchAgent
                context_aware_deep_search   # Context-aware DeepSearchAgent
            ]
        )

        return research_coordinator

    def _create_context_aware_agent(self, base_agent, agent_name: str, user_requirements: str, research_plan: str, focus: str):
        """
        Create a context-aware wrapper around existing agents.

        This method creates a custom BaseAgent that injects user requirements and
        research plan into session state before delegating to the existing agent,
        ensuring proper context propagation.

        Args:
            base_agent: The existing agent (WideSearchAgent or DeepSearchAgent)
            agent_name: Name for the context-aware wrapper
            user_requirements: User's newsletter requirements
            research_plan: Research plan from ResearchPlanner
            focus: Description of this agent's focus area

        Returns:
            Custom BaseAgent that provides context to the existing agent
        """

        class ContextAwareAgentWrapper(BaseAgent):
            def __init__(self, wrapped_agent, requirements, plan, focus_area):
                super().__init__(
                    name=agent_name,
                    description=f"Context-aware wrapper for {wrapped_agent.name} with focus on {focus_area}",
                    tools=[],
                    sub_agents=[wrapped_agent],
                    output_key=wrapped_agent.output_key
                )
                self.wrapped_agent = wrapped_agent
                self.user_requirements = requirements
                self.research_plan = plan
                self.focus_area = focus_area

            async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
                # Inject context into session state before running the wrapped agent
                context_injection = {
                    "user_requirements": self.user_requirements,
                    "research_plan": self.research_plan,
                    "current_focus": self.focus_area,
                    "context_injected_by": self.name
                }

                # Update session state with context
                yield Event(
                    author=self.name,
                    actions=EventActions(
                        state_delta=context_injection
                    )
                )

                # Now run the wrapped agent with proper context
                async for event in self.wrapped_agent.run_async(ctx):
                    yield event

        return ContextAwareAgentWrapper(base_agent, user_requirements, research_plan, focus)

    def create_quality_enhanced_research_loop(self, area_name: str, user_requirements: str, research_plan: str) -> LoopAgent:
        """
        Create LoopAgent wrapper around EXISTING agents with agentic quality assessment.

        This method creates a LoopAgent that coordinates context-aware versions of the
        existing WideSearchAgent and DeepSearchAgent with agentic quality assessment.
        Follows the principle of "Orchestrate, don't duplicate."

        Args:
            area_name: Identifier for this research area
            user_requirements: Natural language user requirements
            research_plan: Research plan from ResearchPlanner

        Returns:
            LoopAgent configured with context-aware existing agents and agentic quality assessment
        """
        # Create context-aware wrappers for existing agents
        context_aware_wide_search = self._create_context_aware_agent(
            base_agent=wide_search_agent,
            agent_name=f"ContextAwareWideSearch_{area_name}",
            user_requirements=user_requirements,
            research_plan=research_plan,
            focus=f"broad coverage for {area_name}"
        )

        context_aware_deep_search = self._create_context_aware_agent(
            base_agent=deep_search_agent,
            agent_name=f"ContextAwareDeepSearch_{area_name}",
            user_requirements=user_requirements,
            research_plan=research_plan,
            focus=f"detailed analysis for {area_name}"
        )

        # Create agentic quality assessment agent for this research area
        quality_agent = QualityAssessmentAgent(
            name=f"AgenticQualityAssessor_{area_name}",
            description=f"Evaluates research quality for {area_name} using LLM intelligence"
        )

        # Use context-aware existing agents with quality assessment
        return LoopAgent(
            name=f"QualityEnhancedResearch_{area_name}",
            sub_agents=[
                context_aware_wide_search,  # Context-aware WideSearchAgent
                context_aware_deep_search,  # Context-aware DeepSearchAgent
                quality_agent               # Agentic quality assessment with escalation
            ],
            max_iterations=int(os.getenv("MAX_RESEARCH_ITERATIONS", "3"))
        )
