"""
ADK-Native Dynamic Research Coordinator for orchestrating existing agents.

This module provides the DynamicResearchCoordinator, a simplified BaseAgent that uses
ADK's built-in orchestration capabilities to coordinate existing WideSearchAgent and
DeepSearchAgent instances. It follows ADK best practices by leveraging the framework's
native session state management and agent coordination patterns.

Key Features:
    - ADK-native direct agent coordination (no custom wrappers)
    - Leverages ADK's built-in session state management
    - Universal flexibility for any newsletter type
    - Direct ParallelAgent and LoopAgent orchestration
    - Trusts existing agents to read session state natively

ADK-Native Principles:
    - Uses ADK's ParallelAgent for direct coordination
    - Trusts framework's session state propagation
    - No manual context injection or custom wrappers
    - Leverages existing agent capabilities without duplication
    - Follows "trust the framework" philosophy

Usage:
    from underlines_adk.agents.dynamic_research_coordinator import DynamicResearchCoordinator
    from google.adk.runners import Runner

    coordinator = DynamicResearchCoordinator(
        name="ADKNativeResearchCoordinator",
        description="Coordinates research using ADK-native patterns"
    )

    runner = Runner(agent=coordinator, ...)

Dependencies:
    - google.adk.agents: BaseAgent, ParallelAgent, LoopAgent
    - google.adk.events: Event
    - .wide_search_agent: WideSearchAgent instance
    - .deep_search_agent: DeepSearchAgent instance
    - .quality_assessment_agent: QualityAssessmentAgent
"""

import os
from typing import AsyncGenerator
from google.adk.agents import BaseAgent, ParallelAgent, LoopAgent
from google.adk.events import Event

from .wide_search_agent import exa_agent as wide_search_agent
from .deep_search_agent import exa_agent as deep_search_agent
from .quality_assessment_agent import QualityAssessmentAgent


class DynamicResearchCoordinator(BaseAgent):
    """
    ADK-native BaseAgent that orchestrates existing agents using framework capabilities.

    This coordinator uses ADK's built-in ParallelAgent and LoopAgent patterns to
    coordinate existing WideSearchAgent and DeepSearchAgent instances. It trusts
    ADK's session state management and avoids custom wrapper complexity.

    The coordinator leverages ADK's native orchestration capabilities, allowing
    existing agents to read session state directly through their instructions,
    maintaining universal flexibility for any newsletter type.
    """

    def __init__(self, name: str = "DynamicResearchCoordinator", description: str | None = None):
        """Initialize the DynamicResearchCoordinator."""
        if description is None:
            description = "Uses LLM intelligence to coordinate research using existing agents"

        super().__init__(
            name=name,
            description=description,
            sub_agents=[],  # We'll create sub-agents dynamically using LLM intelligence
            before_agent_callback=None,
            after_agent_callback=None
        )

    async def _run_async_impl(self, ctx) -> AsyncGenerator[Event, None]:
        """
        Implement ADK-native orchestration logic for dynamic research coordination.

        This method:
        1. Validates required session state (user_requirements, research_plan)
        2. Directly coordinates existing agents using ADK's ParallelAgent
        3. Trusts existing agents to read session state natively
        4. Yields events from the parallel research coordination
        """
        # Validate required session state (ADK pattern)
        user_requirements = ctx.session.state.get("user_requirements", "")
        research_plan = ctx.session.state.get("research_plan", "")

        # Explicit error handling for missing inputs (agentic principle)
        if not user_requirements:
            raise ValueError("No user requirements found for research coordination")
        if not research_plan:
            raise ValueError("No research plan found for research coordination")

        # ADK-native direct agent coordination (no wrappers needed)
        # Existing agents read session state automatically via their instructions
        research_coordinator = ParallelAgent(
            name="DirectResearchCoordination",
            sub_agents=[
                wide_search_agent,  # Reads session state natively
                deep_search_agent   # Reads session state natively
            ]
        )

        # Execute direct coordination using ADK's built-in orchestration
        async for event in research_coordinator.run_async(ctx):
            yield event

    def create_quality_enhanced_research_loop(self, area_name: str = "research") -> LoopAgent:
        """
        Create ADK-native LoopAgent that coordinates existing agents with quality assessment.

        This method creates a LoopAgent that directly coordinates the existing
        WideSearchAgent and DeepSearchAgent with agentic quality assessment.
        Uses ADK's built-in session state management - no manual context injection needed.

        Args:
            area_name: Optional identifier for this research area (default: "research")

        Returns:
            LoopAgent configured with existing agents and agentic quality assessment
        """
        # Create agentic quality assessment agent
        quality_agent = QualityAssessmentAgent(
            name=f"QualityAssessor_{area_name}",
            description=f"Evaluates research quality using LLM intelligence"
        )

        # ADK-native direct agent coordination (agents read session state automatically)
        return LoopAgent(
            name=f"QualityEnhancedResearch_{area_name}",
            sub_agents=[
                wide_search_agent,  # Reads session state natively
                deep_search_agent,  # Reads session state natively
                quality_agent       # Agentic quality assessment with escalation
            ],
            max_iterations=int(os.getenv("MAX_RESEARCH_ITERATIONS", "3"))
        )
